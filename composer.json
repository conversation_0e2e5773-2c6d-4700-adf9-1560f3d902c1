{"name": "bradoc/auditoria", "type": "project", "description": "Auditoria de Dados Publicos", "keywords": ["bradoc", "auditoria"], "license": "private", "require": {"php": "^8.2", "ext-dom": "*", "ext-gd": "*", "ext-mbstring": "*", "ext-zip": "*", "advoor/nova-editor-js": "^3.3", "alexwenzel/nova-dependency-container": "^1.10", "arcanedev/log-viewer": "~10", "archtechx/enums": "^1.1", "barryvdh/laravel-snappy": "^1.0.2", "bensampo/laravel-enum": "^6.6", "bolechen/nova-activitylog": "^0.4.1", "bradoc/lacuna-select": "*", "bradoctech/brandenburg": "^3.0.1", "bradoctech/cardug-users": "@dev", "bradoctech/novatoolpermissions": "^2.0.0", "bradoctech/sapc-nova-ckeditor": "^2.9.13", "brazanation/documents": "2.1.*", "caxy/php-htmldiff": "^0.1.15", "codegreencreative/laravel-samlidp": "5.2.11", "datomatic/nova-detached-actions": "^2.0", "dcasp/dcasp": "*", "dcasp/dcaspformula": "*", "dcasp/fonte-dados": "*", "dillingham/nova-attach-many": "^1.3", "ebess/advanced-nova-media-library": "^4.1.6", "field/cpf-usuario": "*", "field/email-usuario": "*", "field/enunciado-pergunta": "*", "field/nome-periodo-remessa": "*", "field/nome-unidade-gestora": "*", "field/nome-usuario": "*", "field/resposta-texto": "*", "field/tipos-layout": "*", "guzzlehttp/guzzle": "^7.0.1", "h4cc/wkhtmltoimage-amd64": "0.12.x", "h4cc/wkhtmltopdf-amd64": "0.12.x", "inertiajs/inertia-laravel": "^0.6.11", "khalin/nova-link-field": "^1.2", "kirschbaum-development/nova-inline-relationship": "^2.0", "lab404/laravel-impersonate": "^1.7", "lacuna/restpki-client": "^2.9.2", "laravel/framework": "^10.48.2", "laravel/nova": "4.25", "laravel/reverb": "^1.4", "laravel/sanctum": "^3.3", "laravel/scout": "^10.3", "laravel/slack-notification-channel": "^3.4.1", "laravel/tinker": "^2.8.2", "laravellegends/pt-br-validator": "^11", "maatwebsite/excel": "^3.1", "maatwebsite/laravel-nova-excel": "^1.2", "moneyphp/money": "^3.3", "nunomaduro/collision": "^7.0", "nurmuhammet/nova-inputmask": "^1.0", "openspout/openspout": "^4.26", "outl1ne/nova-detached-filters": "^2.0", "outl1ne/nova-multiselect-field": "^4.3", "outl1ne/nova-multiselect-filter": "^4.0", "owenmelbz/nova-radio-field": "^1.0", "pdmfc/nova-info-card": "^1.0", "php-junior/nova-logs": "^1.1", "phpoffice/phpspreadsheet": "^1.29", "rap2hpoutre/fast-excel": "^5.3", "razorcreations/ajax-field": "^0.3.5", "reedware/nova-text-filter": "^2.0.0", "ricontabil/equacao": "*", "ricontabil/regra": "*", "sapc/variavel-conta-governo": "@dev", "sentry/sentry-laravel": "^4.9", "setasign/fpdi": "^2.6", "sietse85/nova-button": "^1.0", "simplesquid/nova-enum-field": "^3.1.0", "slash2nl/nova-back-button": "^1", "smalot/pdfparser": "^2.12", "spatie/browsershot": "^4.0", "spatie/laravel-medialibrary": "^10.1", "spatie/laravel-model-status": "^1.10", "spatie/laravel-responsecache": "^7.4.9", "spatie/pdf-to-text": "^1.4", "spatie/temporary-directory": "^2.2.0", "symfony/dom-crawler": "^5.4", "symfony/expression-language": "^7.0", "titasgailius/search-relations": "^2.0", "webklex/laravel-pdfmerger": "^1.3", "wgenial/numeroporextenso": "^3.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.7", "barryvdh/laravel-ide-helper": "^2.10", "brianium/paratest": "^7.0", "coderello/laravel-nova-lang": "^1.8", "fakerphp/faker": "^1.9.1", "laracasts/generators": "^2.0", "laravel/pint": "^1.21", "laravel/sail": "^1.16", "laravel/telescope": "^5.9", "lucascudo/laravel-pt-br-localization": "1.1.10", "mockery/mockery": "^1.4.2", "nunomaduro/phpinsights": "^2.11", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.4.0", "phpmd/phpmd": "^2.15", "phpunit/phpunit": "^10.1", "slevomat/coding-standard": "^8.15"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true, "dealerdirect/phpcodesniffer-composer-installer": true}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"files": ["database/migrations/2021_07_23_161749_create_servidores_nome_cpf_view.php", "database/migrations/2021_07_23_165442_create_cruzamento_folha_pagamento_view.php", "database/migrations/2021_07_23_185218_create_cruzamento_folha_pagamento_detalhe_view.php", "database/migrations/2021_07_28_121240_fix_cruzamento_folha_pagamento_view.php", "database/migrations/2021_07_28_183558_fix_cruzamento_folha_pagamento_detalhe_view.php", "database/migrations/2021_09_13_084703_add_nome_cruzamento_folha_pagamento_detalhe_view.php", "database/migrations/2021_09_21_110145_remove_duplicates_cruzamento_folha_pagamento_view.php", "app/Helpers/JobsHelper.php"], "psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "repositories": {"nova": {"type": "composer", "url": "https://nova.laravel.com"}, "0": {"type": "path", "url": "./nova-components/LacunaSelect"}, "1": {"type": "path", "url": "./nova-components/NomeUnidadeGestora"}, "2": {"type": "path", "url": "./nova-components/NomeUsuario"}, "3": {"type": "path", "url": "./nova-components/CpfUsuario"}, "4": {"type": "path", "url": "./nova-components/EmailUsuario"}, "5": {"type": "vcs", "url": "https://github.com/bradoctech/laravel-nova-nested-form"}, "6": {"type": "path", "url": "./nova-components/CardugUsers"}, "7": {"type": "path", "url": "./nova-components/NomePeriodoRemessa"}, "8": {"type": "path", "url": "./nova-components/EnunciadoPergunta"}, "9": {"type": "path", "url": "./nova-components/EnunciadoPergunta"}, "10": {"type": "path", "url": "./nova-components/TiposLayout"}, "11": {"type": "path", "url": "./nova-components/RespostaTexto"}, "12": {"type": "path", "url": "./nova-components/Equacao"}, "13": {"type": "path", "url": "./nova-components/Equacao"}, "14": {"type": "path", "url": "./nova-components/Regra"}, "15": {"type": "vcs", "url": "https://github.com/bradoctech/attach-belongs-many"}, "16": {"type": "path", "url": "./nova-components/FonteDados"}, "17": {"type": "path", "url": "./nova-components/Dcasp"}, "18": {"type": "path", "url": "./nova-components/Dcaspformula"}, "19": {"type": "vcs", "url": "https://github.com/bradoctech/brandenburg"}, "20": {"type": "vcs", "url": "https://github.com/bradoctech/novatoolpermissions"}, "21": {"type": "vcs", "url": "https://github.com/bradoctech/sapc-nova-ckeditor"}, "22": {"type": "path", "url": "./nova-components/VariavelContaGoverno"}}}