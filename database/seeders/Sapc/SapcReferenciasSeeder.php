<?php

namespace Database\Seeders\Sapc;

use App\Enums\Sapc\StatusModeloFormulario;
use App\Models\Sapc\ModeloFormulario;
use Illuminate\Database\Seeder;

class SapcReferenciasSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $textoReferencias = '<h1>13. Referências</h1>
        <p style="text-align:justify;">Este capítulo reúne as fontes complementares utilizadas para embasar a análise técnica. Trata-se de materiais que contribuíram de forma relevante para o aprofundamento do conhecimento sobre os temas abordados, servindo de base teórica, normativa ou metodológica para a análise das prestações de contas de governo municipal.</p>

        <p style="text-align:justify;">As referências incluem manuais técnicos, legislações vigentes, livros especializados, artigos, documentos institucionais e conteúdos disponibilizados em sites oficiais de órgãos de controle, instituições públicas, organismos internacionais e demais fontes reconhecidamente confiáveis.</p>

        <p style="text-align:justify;">O objetivo é garantir a rastreabilidade das informações utilizadas, promover a transparência do processo de análise e permitir que o leitor interessado possa consultar as mesmas fontes para aprofundamento.</p>

        <div class="page-break" style="page-break-after:always;"><span style="display:none;">&nbsp;</span></div>

        <h2>Lista de Referências</h2>
        $_marcador{Referencia}';

        ModeloFormulario::updateOrCreate(
            ['nome' => 'Referências'],
            [
                'texto' => $textoReferencias,
                'status' => StatusModeloFormulario::Ativo,
                'versao' => 1,
                'referencias' => true,
            ]
        );
    }
}
